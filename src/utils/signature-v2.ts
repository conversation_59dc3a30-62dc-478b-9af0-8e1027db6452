/**
 * v2接口签名生成工具
 * 专门处理当贝AI v2接口的签名算法
 */

import { createHash } from 'crypto';
import { SignatureParams, DeviceConfig } from '../types';

/**
 * v2接口签名生成器
 * 基于详细分析，v2接口使用与v1不同的签名算法
 */
export class SignatureV2Utils {
  /**
   * 生成v2接口签名
   * 
   * 由于v2接口的签名算法无法完全破解，我们采用多种策略：
   * 1. 尝试已知的可能算法
   * 2. 提供降级方案
   * 3. 详细的错误日志
   * 
   * @param params 签名参数
   * @param extra 额外配置
   * @returns 签名字符串
   */
  public static generateV2Signature(
    params: SignatureParams,
    extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): string {
    // 策略1: 尝试使用METHOD + PATH的算法（调用流程.md中提到的）
    const methodPathSignature = this.tryMethodPathAlgorithm(params);
    
    // 策略2: 尝试使用简化的算法（仅timestamp + nonce）
    const simplifiedSignature = this.trySimplifiedAlgorithm(params);
    
    // 策略3: 尝试使用设备相关的算法
    const deviceBasedSignature = this.tryDeviceBasedAlgorithm(params, extra);
    
    // 在调试模式下输出所有尝试的签名
    if (process.env['NODE_ENV'] === 'development') {
      console.log('🔐 v2接口签名生成尝试:');
      console.log('  - 策略1 (METHOD+PATH):', methodPathSignature);
      console.log('  - 策略2 (简化算法):', simplifiedSignature);
      console.log('  - 策略3 (设备相关):', deviceBasedSignature);
      console.log('  - 选择策略1作为默认方案');
    }
    
    // 默认返回METHOD + PATH算法的结果
    // 如果失败，错误处理会在上层进行
    return methodPathSignature;
  }

  /**
   * 策略1: METHOD + PATH算法
   * 基于调用流程.md中的描述
   */
  private static tryMethodPathAlgorithm(params: SignatureParams): string {
    const { timestamp, nonce, method = 'POST', url = '' } = params;
    
    // 规范化串采用 METHOD + 空格 + PATH 的格式
    const normalized = `${method.toUpperCase()} ${url}`;
    const signString = `${timestamp}${normalized}${nonce}`;
    
    return createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();
  }

  /**
   * 策略2: 简化算法
   * 仅使用timestamp + nonce，适用于某些特殊情况
   */
  private static trySimplifiedAlgorithm(params: SignatureParams): string {
    const { timestamp, nonce } = params;
    
    const signString = `${timestamp}${nonce}`;
    
    return createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();
  }

  /**
   * 策略3: 设备相关算法
   * 包含设备ID或其他设备信息
   */
  private static tryDeviceBasedAlgorithm(
    params: SignatureParams,
    extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): string {
    const { timestamp, nonce, deviceId } = params;
    
    // 如果有设备ID，尝试包含在签名中
    let signString = `${timestamp}`;
    
    if (deviceId) {
      signString += deviceId;
    }
    
    if (extra?.appVersion) {
      signString += extra.appVersion;
    }
    
    signString += nonce;
    
    return createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();
  }

  /**
   * 检测URL是否为v2接口
   * @param url 请求URL
   * @returns 是否为v2接口
   */
  public static isV2Interface(url: string): boolean {
    return url.includes('/v2/') || 
           url.includes('chatApi') || 
           url.includes('/chat');
  }

  /**
   * 获取v2接口的错误处理建议
   * @param url 请求URL
   * @returns 错误处理建议
   */
  public static getV2ErrorSuggestions(url: string): string[] {
    const suggestions = [
      '当前v2接口使用了未知的签名算法，可能需要特殊的密钥或处理逻辑',
      '建议检查当贝AI官方文档是否有更新的签名规则',
      '可以尝试使用浏览器开发者工具抓取真实的签名参数进行对比',
      '如果问题持续，建议联系当贝AI技术支持获取最新的API文档'
    ];
    
    if (url.includes('chat')) {
      suggestions.push('聊天接口可能需要特殊的流式处理或WebSocket连接');
    }
    
    return suggestions;
  }

  /**
   * 生成v2接口的调试信息
   * @param params 签名参数
   * @returns 调试信息
   */
  public static generateDebugInfo(params: SignatureParams): {
    url: string;
    isV2: boolean;
    strategies: Array<{
      name: string;
      signature: string;
      description: string;
    }>;
  } {
    const url = params.url || '';
    const isV2 = this.isV2Interface(url);
    
    const strategies = [
      {
        name: 'METHOD+PATH',
        signature: this.tryMethodPathAlgorithm(params),
        description: '使用HTTP方法和路径的组合'
      },
      {
        name: 'SIMPLIFIED',
        signature: this.trySimplifiedAlgorithm(params),
        description: '仅使用时间戳和随机数'
      },
      {
        name: 'DEVICE_BASED',
        signature: this.tryDeviceBasedAlgorithm(params),
        description: '包含设备信息的算法'
      }
    ];
    
    return {
      url,
      isV2,
      strategies
    };
  }
}
