/**
 * v2接口签名生成工具
 * 专门处理当贝AI v2接口的签名算法
 */

import { createHash } from 'crypto';
import { SignatureParams, DeviceConfig } from '../types';
import { WasmSignatureEmulator } from './wasm-signature-emulator';

/**
 * v2接口签名生成器
 * 基于WebAssembly模块分析，实现兼容的签名算法
 */
export class SignatureV2Utils {
  private static wasmEmulator: WasmSignatureEmulator;

  /**
   * 初始化WASM签名模拟器
   */
  private static initWasmEmulator(): void {
    if (!this.wasmEmulator) {
      this.wasmEmulator = new WasmSignatureEmulator({
        debug: process.env['NODE_ENV'] === 'development',
        strategy: 'hybrid',
        secretKey: 'dangbei_ai_v2_secret_2024'
      });
    }
  }
  /**
   * 生成v2接口签名
   *
   * 基于对sign_bg.wasm的深入分析，实现多策略签名生成：
   * 1. WASM模拟算法（主要策略）
   * 2. 传统算法降级方案
   * 3. 设备相关的增强算法
   *
   * @param params 签名参数
   * @param extra 额外配置
   * @returns 签名字符串
   */
  public static generateV2Signature(
    params: SignatureParams,
    extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): string {
    this.initWasmEmulator();

    try {
      // 策略1: 使用WASM模拟算法（基于分析结果的最佳实现）
      const wasmSignature = this.tryWasmEmulationAlgorithm(params, extra);

      // 策略2: 传统METHOD + PATH算法（降级方案）
      const methodPathSignature = this.tryMethodPathAlgorithm(params);

      // 策略3: 简化算法（最后的降级方案）
      const simplifiedSignature = this.trySimplifiedAlgorithm(params);

      // 在调试模式下输出所有尝试的签名
      if (process.env['NODE_ENV'] === 'development') {
        console.log('🔐 v2接口签名生成策略:');
        console.log('  - 策略1 (WASM模拟):', wasmSignature.substring(0, 8) + '...');
        console.log('  - 策略2 (METHOD+PATH):', methodPathSignature.substring(0, 8) + '...');
        console.log('  - 策略3 (简化算法):', simplifiedSignature.substring(0, 8) + '...');
        console.log('  - 选择WASM模拟算法作为主要方案');
      }

      // 优先返回WASM模拟算法的结果
      return wasmSignature;

    } catch (error) {
      console.warn('⚠️ WASM模拟算法失败，使用降级方案:', (error as Error).message);

      // 降级到传统算法
      return this.tryMethodPathAlgorithm(params);
    }
  }

  /**
   * 策略1: WASM模拟算法
   * 基于对sign_bg.wasm的分析实现的模拟算法
   */
  private static tryWasmEmulationAlgorithm(
    params: SignatureParams,
    extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): string {
    const { timestamp, nonce, method = 'POST', url = '', bodyRaw = '', data } = params;

    // 构建请求数据字符串（模拟WASM中的第一个参数）
    let requestData = '';
    if (method === 'GET') {
      // GET请求使用查询参数
      const qIndex = url.indexOf('?');
      requestData = qIndex !== -1 ? url.substring(qIndex + 1) : '';
    } else if (method === 'POST') {
      // POST请求使用请求体，优先使用bodyRaw，否则序列化data
      requestData = bodyRaw || (data ? JSON.stringify(data) : '');
    }

    // 添加设备信息到请求数据中
    if (extra?.appVersion) {
      requestData += `&appVersion=${extra.appVersion}`;
    }
    if (extra?.clientVersion) {
      requestData += `&clientVersion=${extra.clientVersion}`;
    }
    if (params.deviceId) {
      requestData += `&deviceId=${params.deviceId}`;
    }

    // 构建时间戳/nonce字符串（模拟WASM中的第二个参数）
    const timestampNonce = `${timestamp}:${nonce}`;

    // 调用WASM模拟器生成签名
    const result = this.wasmEmulator.getSign(requestData, timestampNonce);

    return result.signature;
  }

  /**
   * 策略1: METHOD + PATH算法
   * 基于调用流程.md中的描述
   */
  private static tryMethodPathAlgorithm(params: SignatureParams): string {
    const { timestamp, nonce, method = 'POST', url = '' } = params;
    
    // 规范化串采用 METHOD + 空格 + PATH 的格式
    const normalized = `${method.toUpperCase()} ${url}`;
    const signString = `${timestamp}${normalized}${nonce}`;
    
    return createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();
  }

  /**
   * 策略2: 简化算法
   * 仅使用timestamp + nonce，适用于某些特殊情况
   */
  private static trySimplifiedAlgorithm(params: SignatureParams): string {
    const { timestamp, nonce } = params;
    
    const signString = `${timestamp}${nonce}`;
    
    return createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();
  }

  /**
   * 策略3: 设备相关算法
   * 包含设备ID或其他设备信息
   */
  private static tryDeviceBasedAlgorithm(
    params: SignatureParams,
    extra?: Partial<Pick<DeviceConfig, 'appType' | 'appVersion' | 'clientVersion' | 'lang'>>
  ): string {
    const { timestamp, nonce, deviceId } = params;
    
    // 如果有设备ID，尝试包含在签名中
    let signString = `${timestamp}`;
    
    if (deviceId) {
      signString += deviceId;
    }
    
    if (extra?.appVersion) {
      signString += extra.appVersion;
    }
    
    signString += nonce;
    
    return createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();
  }

  /**
   * 检测URL是否为v2接口
   * @param url 请求URL
   * @returns 是否为v2接口
   */
  public static isV2Interface(url: string): boolean {
    return url.includes('/v2/') || 
           url.includes('chatApi') || 
           url.includes('/chat');
  }

  /**
   * 获取v2接口的错误处理建议
   * @param url 请求URL
   * @returns 错误处理建议
   */
  public static getV2ErrorSuggestions(url: string): string[] {
    const suggestions = [
      '当前v2接口使用了未知的签名算法，可能需要特殊的密钥或处理逻辑',
      '建议检查当贝AI官方文档是否有更新的签名规则',
      '可以尝试使用浏览器开发者工具抓取真实的签名参数进行对比',
      '如果问题持续，建议联系当贝AI技术支持获取最新的API文档'
    ];
    
    if (url.includes('chat')) {
      suggestions.push('聊天接口可能需要特殊的流式处理或WebSocket连接');
    }
    
    return suggestions;
  }

  /**
   * 生成v2接口的调试信息
   * @param params 签名参数
   * @returns 调试信息
   */
  public static generateDebugInfo(params: SignatureParams): {
    url: string;
    isV2: boolean;
    strategies: Array<{
      name: string;
      signature: string;
      description: string;
    }>;
  } {
    const url = params.url || '';
    const isV2 = this.isV2Interface(url);
    
    const strategies = [
      {
        name: 'WASM_EMULATION',
        signature: this.tryWasmEmulationAlgorithm(params),
        description: '基于WebAssembly模块分析的模拟算法'
      },
      {
        name: 'METHOD+PATH',
        signature: this.tryMethodPathAlgorithm(params),
        description: '使用HTTP方法和路径的组合'
      },
      {
        name: 'SIMPLIFIED',
        signature: this.trySimplifiedAlgorithm(params),
        description: '仅使用时间戳和随机数'
      },
      {
        name: 'DEVICE_BASED',
        signature: this.tryDeviceBasedAlgorithm(params),
        description: '包含设备信息的算法'
      }
    ];
    
    return {
      url,
      isV2,
      strategies
    };
  }
}
